import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PageConfig } from "@/lib/types/page-config";
import { PlusCircle, ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface DynamicPageHeaderProps {
  config: PageConfig;
  onCreateNew?: () => void;
  showBackButton?: boolean;
  backButtonUrl?: string;
  backButtonText?: string;
  actionButtons?: React.ReactNode;
}

/**
 * Header component for dynamic pages
 */
export function DynamicPageHeader({
  config,
  onCreateNew,
  showBackButton = false,
  backButtonUrl,
  backButtonText = "Back",
  actionButtons,
}: Readonly<DynamicPageHeaderProps>) {
  const navigate = useNavigate();
  const { hasPagePermission } = useAuth();
  const handleBack = () => {
    if (backButtonUrl) {
      navigate(backButtonUrl);
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-6">
      <div className="flex flex-col">
        <div className="flex items-center gap-2">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              className="mr-2"
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              {backButtonText}
            </Button>
          )}
          <h1 className="text-3xl font-bold tracking-tight">{config.title}</h1>
        </div>
        {/* {config.description && (
          <p className="text-muted-foreground mt-1">{config.description}</p>
        )} */}
      </div>
      <div className="flex items-center gap-2">
        {actionButtons}
        {config.type === "list" && onCreateNew && hasPagePermission(config.id, "create") && (
          <Button onClick={onCreateNew}>
            <PlusCircle className="h-4 w-4 mr-2" />
            {config.addLabel ?? `Add ${config.entityName}`}
          </Button>
        )}
      </div>
    </div>
  );
}

export default DynamicPageHeader;
