import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";
import { FormService } from "@/lib/services/form-service";
import { useToast } from "@/hooks/use-toast";
import { ErrorResponse } from "@/lib/types/api";

interface UseFormOperationsProps {
  id?: string;
}

export function useFormOperations({ id }: UseFormOperationsProps) {
  const navigate = useNavigate();
  const isNewForm = !id;
  const { toast } = useToast();

  const [form, setForm] = useState<FormSchema>({
    id: id ?? "",
    name: "",
    description: "",
    status: "DRAFT",
    components: [],
    formType: "APPLICATION",
    projectType: "CAPITAL",
    version: "",
  });

  const [isLoading, setIsLoading] = useState(!isNewForm);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  useEffect(() => {
    const loadForm = async () => {
      if (isNewForm) {
        setIsLoading(false);
        return;
      }

      try {
        const formData = await FormService.getFormById(id);
        if (formData) {
          setForm(formData);
        } else {
          // Form not found, redirect to 404
          navigate("/not-found");
        }
      } catch (error) {
        console.error("Failed to load form:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadForm();
  }, [id, isNewForm, navigate]);

  const handleFormChange = (field: keyof FormSchema, value: any) => {
    setForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleComponentsChange = (components: FormComponent[]) => {
    setForm((prev) => ({
      ...prev,
      components,
    }));
  };

  // Validation logic for required fields
  const isFormValid = () => {
    return !!(form.name?.trim() && form.projectType?.trim());
  };

  const handleSave = async () => {
    // Validate required fields before saving
    if (!isFormValid()) {
      toast({
        title: "Validation Error",
        description:
          "Please fill in all required fields (Form Name and Project Type).",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    try {
      if (isNewForm) {
        const newForm = await FormService.createForm(form);
        toast({
          title: "Form created",
          description: "Your form has been created successfully.",
        });
        navigate(`/forms/form/${newForm.id}`);
      } else {
        await FormService.updateForm(id, form);
        toast({
          title: "Form saved",
          description: "Your form has been saved successfully.",
        });
      }
    } catch (error) {
      console.error("Failed to save form:", error);
      toast({
        title: "Error",
        description: `${(error as ErrorResponse)?.details} `,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (!id) return;

    setIsPublishing(true);

    try {
      // Validate form before publishing
      if (!form.name.trim()) {
        toast({
          title: "Validation Error",
          description: "Form name is required before publishing.",
          variant: "destructive",
        });
        return;
      }

      if (form.components.length === 0) {
        toast({
          title: "Validation Error",
          description:
            "Form must have at least one component before publishing.",
          variant: "destructive",
        });
        return;
      }

      // Update form status to active
      const updatedForm = await FormService.activateForm(id);

      // Update local state
      setForm(updatedForm);

      toast({
        title: "Form published",
        description: "Your form is now active and available for applications.",
      });
    } catch (error) {
      console.error("Failed to publish form:", error);
      toast({
        title: "Error",
        description: `${(error as ErrorResponse)?.details} `,
        variant: "destructive",
      });
    } finally {
      setIsPublishing(false);
    }
  };

  return {
    form,
    isLoading,
    isSaving,
    isPublishing,
    isNewForm,
    isFormValid: isFormValid(),
    handleFormChange,
    handleComponentsChange,
    handleSave,
    handlePublish,
  };
}
