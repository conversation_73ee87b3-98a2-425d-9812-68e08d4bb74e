import { useForm, UseFormReturn } from "react-hook-form";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormSubmission } from "@/lib/types/submission";
import { useAuth } from "@/contexts/AuthContext";
import { useStepNavigation } from "./useStepNavigation";
import { useFormDataLoader } from "./useFormDataLoader";
import { useFormSubmissionOperations } from "./useFormSubmissionOperations";
import { useFormState } from "./useFormState";
import { FormStep } from "@/lib/utils/form-structure-utils";

interface UseFormSubmissionProps {
  formId: string;
  submissionId?: string; // Optional - if provided, will load existing submission
  projectRef?: string;
}

interface UseFormSubmissionReturn {
  form: FormSchema | null;
  submission: FormSubmission | null;
  methods: UseFormReturn;
  isLoading: boolean;
  isSaving: boolean;
  isSubmitting: boolean;
  formStatus: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  };
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  steps: FormStep[];
  isMultiStep: boolean;
  currentStep: number;
  currentStepData: FormStep;
  nextStep: () => Promise<void>;
  prevStep: () => void;
  saveProgress: () => Promise<boolean>;
  submitForm: () => Promise<boolean>;
}

/**
 * Custom hook for form submission functionality
 * Coordinates form loading, state management, step navigation, and form operations
 */
export function useFormSubmission({
  formId,
  submissionId,
  projectRef,
}: UseFormSubmissionProps): UseFormSubmissionReturn {
  const { user } = useAuth();

  // Create a form instance with react-hook-form
  const methods = useForm({
    mode: "onSubmit",
    criteriaMode: "all", // Show all validation errors
  });

  // Initialize form state
  const {
    form,
    submission,
    formStatus,
    steps,
    isMultiStep,
    setForm,
    setSubmission,
    setFormStatus: updateFormStatus,
  } = useFormState({
    initialForm: null,
    initialSubmission: null,
  });

  // Create a React.Dispatch compatible setFormStatus function
  const setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  > = (value) => {
    if (typeof value === "function") {
      const updater = value;
      updateFormStatus(updater(formStatus));
    } else {
      updateFormStatus(value);
    }
  };

  // Load form and submission data
  const { isLoading } = useFormDataLoader({
    formId,
    projectRef,
    submissionId,
    methods,
    user,
    setFormStatus,
    setForm,
    setSubmission,
  });

  // Use step navigation
  const {
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    commitCurrentStepData,
  } = useStepNavigation({
    steps,
    methods,
    setFormStatus,
    saveProgress: async () => {
      if (saveProgress) {
        return await saveProgress();
      }
      return false;
    },
  });

  // Form operations (save and submit)
  const { isSaving, isSubmitting, saveProgress, submitForm } =
    useFormSubmissionOperations({
      form,
      submission,
      methods,
      user,
      setFormStatus,
      commitCurrentStepData,
      setSubmission,
      projectRef,
    });

  return {
    form,
    submission,
    methods,
    isLoading,
    isSaving,
    isSubmitting,
    formStatus,
    setFormStatus,
    steps,
    isMultiStep,
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    saveProgress,
    submitForm,
  };
}
