import React, { useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { MobileHeader } from "./MobileHeader";
import { MobileSidebar } from "./MobileSidebar";
import { DesktopSidebar } from "./DesktopSidebar";
import { MainContent } from "./MainContent";
import { useBackendNavigation } from "@/hooks/useBackendNavigation";
import { useSidebar } from "@/hooks/useSidebar";

/**
 * Main layout component that provides the overall structure for the application
 */
const MainLayout: React.FC = () => {
  // Get authentication state
  const { user, logout, isAuthenticated, hasPermission, hasPagePermission, globalPermissions } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Handle role-based access control with global permissions
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    // Map routes to page IDs for global permission checking
    const routeToPageIdMap: Record<string, string> = {
      "/forms": "forms",
      "/forms/new": "forms",
      "/submissions": "application-forms",
      "/projects": "projects",
      "/funding-rounds": "funding-round",
      "/users": "users",
      "/documents": "documents",
    };

    // Find matching page ID for current route
    const currentPageId = Object.entries(routeToPageIdMap).find(([route]) =>
      location.pathname.startsWith(route)
    )?.[1];

    if (currentPageId) {
      // Check global page permission first
      if (globalPermissions && !hasPagePermission(currentPageId, "list")) {
        navigate("/unauthorized", { replace: true });
        return;
      }
    }
  }, [location.pathname, hasPermission, hasPagePermission, globalPermissions, navigate, isAuthenticated, user]);

  // Get sidebar state
  const {
    sidebarOpen,
    mobileMenuOpen,
    toggleSidebar,
    toggleMobileMenu,
    setMobileMenuOpen,
  } = useSidebar();

  // Get navigation items from backend
  const { navItems } = useBackendNavigation();

  return (
    <div className="flex min-h-screen flex-col">
      {/* Mobile Header */}
      <MobileHeader
        isAuthenticated={isAuthenticated}
        user={user}
        toggleMobileMenu={toggleMobileMenu}
      />

      {/* Mobile Sidebar - Only shown when authenticated */}
      {isAuthenticated && (
        <MobileSidebar
          isOpen={mobileMenuOpen}
          user={user}
          navItems={navItems}
          onClose={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Desktop Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar - Only shown when authenticated */}
        {isAuthenticated && (
          <DesktopSidebar
            isOpen={sidebarOpen}
            user={user}
            navItems={navItems}
            onToggle={toggleSidebar}
          />
        )}

        {/* Main Content */}
        <MainContent
          isAuthenticated={isAuthenticated}
          user={user}
          logout={logout}
        >
          <Outlet />
        </MainContent>
      </div>
    </div>
  );
};

export default React.memo(MainLayout);
