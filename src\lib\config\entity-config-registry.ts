import {
  EntityConfig,
  FormPageConfig,
  ListPageConfig,
  ParameterConfig,
  CustomDialogsConfig,
} from "@/lib/types/page-config";
import { Command, FileSpreadsheet, FileUp, Users } from "lucide-react";
import { apiClient } from "../api/api-client";
import { GetAllResponses } from "@/lib/types/api.ts";
import { User } from "@/lib/types/user.ts";
// import { DocumentService } from "../services/document-service";
// import { toast } from "@/hooks/use-toast";

const entityConfigs: Record<string, EntityConfig> = {
  // Document entity configuration
  Document: {
    // Base entity information
    id: "documents",
    entityName: "Document",
    title: "Documents",
    description: "Manage project documents",
    endpoints: {
      list: "/documents",
      get: "/documents",
      create: "/documents",
      update: "/documents",
      delete: "/documents",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "id",
        name: "id",
        type: "text",
        label: "Document ID",
        disabled: true,
      },
      {
        id: "fileName",
        name: "fileName",
        type: "text",
        label: "File Name",
        required: true,
      },
      {
        id: "parentObjectId",
        name: "parentObjectId",
        type: "text",
        label: "Parent Object ID",
        disabled: true,
      },
      {
        id: "parentObjectType",
        name: "parentObjectType",
        type: "text",
        label: "Parent Object Type",
        disabled: true,
      },
      {
        id: "fileType",
        name: "fileType",
        type: "text",
        label: "File Type",
        disabled: true,
      },
      {
        id: "isActive",
        name: "isActive",
        type: "checkbox",
        label: "Is Active",
      },
      {
        id: "projectRef",
        name: "projectRef",
        type: "text",
        label: "Project Reference",
        required: true,
      },
      {
        id: "tag",
        name: "tag",
        type: "select",
        label: "Tag",
        required: true,
        options: [
          { value: "APPLICATION", label: "Application" },
          { value: "CLARIFICATION", label: "Clarification" },
          { value: "CASE_STUDY", label: "Case Study" },
          {
            value: "FINAL_OPTIMISATION_REPORTS",
            label: "Final Optimisation Reports",
          },
          { value: "GRANT_CLAIM", label: "Grant Claim" },
          {
            value: "MONITORING_AND_REPORTING",
            label: "Monitoring and Reporting",
          },
        ],
      },
      {
        id: "createdBy",
        name: "createdBy",
        type: "text",
        label: "Created By",
        disabled: true,
      },
      {
        id: "createdAt",
        name: "createdAt",
        type: "date",
        label: "Created At",
        disabled: true,
      },
    ],

    // List configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "Document ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
        },
        {
          id: "fileName",
          header: "File Name",
          accessorKey: "fileName",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
        },
        {
          id: "tag",
          header: "Tag",
          accessorKey: "tag",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
        },
        {
          id: "isActive",
          header: "Active",
          accessorKey: "isActive",
          type: "custom",
          enableSorting: true,
          enableColumnFilter: true,
          cell: ({ row }) => {
            const isActive = row.getValue("isActive") as boolean;
            return isActive ? "Active" : "Inactive";
          },
        },
      ],
      actions: [
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: false,
      // Row selection configuration
      // enableRowSelection: true,
      // enableMultiRowSelection: true,
      // Bulk actions configuration
      // bulkActions: [
      //   {
      //     id: "bulk-download",
      //     label: "Download Selected",
      //     icon: Download,
      //     handler: async (selectedRowIds: string[], _selectedRows: any[]) => {
      //       try {
      //         if (selectedRowIds.length === 0) {
      //           toast({
      //             title: "No documents selected",
      //             description:
      //               "Please select at least one document to download.",
      //             variant: "destructive",
      //           });
      //           return;
      //         }

      //         if (selectedRowIds.length === 1) {
      //           // Download single document
      //           await DocumentService.downloadDocument(selectedRowIds[0]);
      //           toast({
      //             title: "Download started",
      //             description: "Your document download has started.",
      //           });
      //         } else {
      //           // Download multiple documents as ZIP
      //           const zipFileName = `documents-${
      //             new Date().toISOString().split("T")[0]
      //           }.zip`;
      //           await DocumentService.downloadMultipleDocuments(
      //             selectedRowIds,
      //             zipFileName
      //           );
      //           toast({
      //             title: "Download started",
      //             description: `Downloading ${selectedRowIds.length} documents as ZIP file.`,
      //           });
      //         }
      //       } catch (error) {
      //         console.error("Error downloading documents:", error);
      //         toast({
      //           title: "Download failed",
      //           description: "Failed to download documents. Please try again.",
      //           variant: "destructive",
      //         });
      //       }
      //     },
      //     variant: "default",
      //     requireConfirmation: false,
      //   },
      // ],
    },

    // Form configuration
    formConfig: {
      submitButtonText: "Upload Document",
      cancelButtonText: "Cancel",
      successMessage: "Document Uploaded successfully",
      errorMessage: "Failed to Upload document",
      addLabel: "Upload Document",
      isMultiStep: false,
    },
  },

  // Project entity configuration
  Project: {
    // Base entity information
    id: "projects",
    entityName: "Project",
    title: "Projects",
    description: "Manage your projects",
    endpoints: {
      list: "/projects",
      get: "/projects",
      create: "/projects",
      update: "/projects",
      delete: "/projects",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "projectRef",
        name: "projectRef",
        type: "text",
        label: "Project Ref",
        disabled: true,
      },
      {
        id: "name",
        name: "name",
        type: "text",
        label: "Project Name",
        required: true,
      },
      {
        id: "description",
        name: "description",
        type: "text",
        label: "Description",
      },
      {
        id: "status",
        name: "status",
        type: "select",
        label: "Status",
        options: [
          { label: "APPLICATION DRAFT", value: "APPLICATION_DRAFT" },
          { label: "APPLICATION SUBMITTED", value: "APPLICATION_SUBMITTED" },
       /*   {
            label: "APPLICATION UNDER ASSESSMENT - INTERNAL REVIEW",
            value: "APPLICATION_UNDER_ASSESSMENT_INTERNAL_REVIEW",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - CLARIFICATION RAISED",
            value: "APPLICATION_UNDER_ASSESSMENT_CLARIFICATION_RAISED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - ASSESSED",
            value: "APPLICATION_UNDER_ASSESSMENT_ASSESSED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - APPROVED",
            value: "APPLICATION_UNDER_ASSESSMENT_APPROVED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - AWARDED",
            value: "APPLICATION_UNDER_ASSESSMENT_AWARDED",
          },
          { label: "FUNDING AWARDED", value: "FUNDING_AWARDED" },
          { label: "PROJECT COMPLETE", value: "PROJECT_COMPLETE" },
          { label: "PROJECT COMMISSIONED", value: "PROJECT_COMMISSIONED" },*/
        ],
      },

      {
        id: "organisationType",
        name: "organisationType",
        type: "select",
        label: "Organisation Type",
        required: true,

        options: [
          { label: "LOCAL AUTHORITY", value: "LOCAL_AUTHORITY" },
          {
            label: "INTER DEPARTMENTAL TRANSFER",
            value: "INTER_DEPARTMENTAL_TRANSFER",
          },
          { label: "PRIVATE", value: "PRIVATE" },

          { label: "OTHER", value: "OTHER" },
        ],
      },
      {
        id: "applicationType",
        name: "applicationType",
        type: "select",
        label: "Application Type",
        required: true,
        options: [
          { label: "CAPITAL", value: "CAPITAL" },
          { label: "REVENUE", value: "REVENUE" },
        ],
      },

      {
        id: "applicantOrganisation",
        name: "applicantOrganisation",
        type: "text",
        label: "Applicant Organisation",
        required: true,
      },
      {
        id: "fundingRoundId",
        name: "fundingRoundId",
        type: "select",
        label: "Funding Round Id",
        required: true,
        disabled: true,
        autoFillConfig: {
          resolver: async (context) => {
            // Check if we're editing existing data
            if (context.initialData?.fundingRoundId) {
              const response = await context.apiClient.get<{
                id: string;
                closeDate: string;
                openDate: string;
                roundNumber: string;
              }>(`/fundingRounds/${context.initialData.fundingRoundId}`);
              return response.data;
            } else {
              // Get active funding round for new records
              const response = await context.apiClient.get<{
                id: string;
                closeDate: string;
                openDate: string;
                roundNumber: string;
              }>("/fundingRounds/active");
              return response.data;
            }
          },
          onlyOnInitialLoad: true,
          createOptions: true,
          optionsFormatter: (value) => [
            {
              label: `Round ${value.roundNumber} (${new Date(
                value.openDate
              ).toLocaleDateString()} - ${new Date(
                value.closeDate
              ).toLocaleDateString()})`,
              value: value.id,
            },
          ],
          transform: (value) => value.id, // Set the ID as the field value
        },
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "Project Ref",
          accessorKey: "projectRef",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 150,
        },
        {
          id: "name",
          header: "Project Name",
          accessorKey: "name",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "status",
          header: "Status",
          accessorKey: "status",
          type: "status",
          enableSorting: true,
          enableColumnFilter: true,
          width: 350,
          formatOptions: {
            showIcon: true,
            size: "md",
          },
        },
        {
          id: "applicationType",
          header: "Application Type",
          accessorKey: "applicationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },

        {
          id: "organisationType",
          header: "Organisation Type",
          accessorKey: "organisationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "applicantOrganisation",
          header: "Applicant Organisation",
          accessorKey: "applicantOrganisation",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
        {
          id: "application",
          label: "go to application",
          icon: Command,
          action: "custom",
          handler: async (row) => {
            const response = await apiClient.get<{ id: string }>(
              `/forms/actives?formType=APPLICATION&projectType=${row.applicationType}`
            );
            // Navigate to the application page
            window.location.href = `/applications/${response.data.id}/submit/${row.projectRef}`;
          },
        },
        {
          id: "attach",
          label: "Attach User to project",
          icon: Users,
          action: "custom",
          handler: (row) => {
            window.location.href = `/projects/UserProject/${row.projectRef}/users`;
          },
        },
        {
          id: "documents",
          label: "go to documents",
          icon: FileUp,
          action: "custom",
          handler: async (row) => {
            window.location.href = `/projects/${row.projectRef}/documents`;
          },
        },
        {
          id: "applicant-submission",
          label: "go to submissions",
          icon: FileSpreadsheet,
          action: "custom",
          handler: async (row) => {
            const response = await apiClient.get<{ id: string }>(
              `/forms/actives?formType=APPLICATION&projectType=${row.applicationType}`
            );
            window.location.href = `/submissions/${response.data.id}/${row.projectRef}`;
          },
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      defaultVisibilityState: {},
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Project",
      cancelButtonText: "Cancel",
      successMessage: "Project saved successfully!",
      errorMessage: "Failed to save project. Please try again.",
      redirectAfterSubmit: "/projects",
      addLabel: "Create Project",
    },
  },

  // FundingRound entity configuration
  FundingRound: {
    // Base entity information
    id: "funding-round",
    entityName: "FundingRound",
    title: "Funding Rounds",
    description: "Manage funding rounds",
    endpoints: {
      list: "/fundingRounds",
      get: "/fundingRounds",
      create: "/fundingRounds",
      update: "/fundingRounds",
      delete: "/fundingRounds",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "roundNumber",
        name: "roundNumber",
        type: "text",
        label: "Round Number",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Round Number is required",
          },
        ],
      },
      {
        id: "openDate",
        name: "openDate",
        type: "date",
        label: "Open Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Open Date is required",
          },
        ],
      },
      {
        id: "closeDate",
        name: "closeDate",
        type: "date",
        label: "Submission Deadline Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Submission Deadline Date is required",
          },
        ],
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 0,
        },
        {
          id: "roundNumber",
          header: "Round Number",
          accessorKey: "roundNumber",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 150,
        },
        {
          id: "openDate",
          header: "Open Date",
          accessorKey: "openDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 200,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
        {
          id: "closeDate",
          header: "Submission Deadline",
          accessorKey: "closeDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 250,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "roundNumber", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: false,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Funding Round",
      cancelButtonText: "Cancel",
      successMessage: "Funding Round saved successfully!",
      errorMessage: "Failed to save funding round. Please try again.",
      redirectAfterSubmit: "/funding-rounds",
      addLabel: "Create Funding Round",
    },
  },
  Form: {
    // Base entity information
    id: "forms",
    entityName: "Form",
    title: "Forms",
    description: "Manage your forms",
    endpoints: {
      list: "/forms",
      get: "/forms",
      create: "/forms",
      update: "/forms",
      delete: "/forms",
    },
    fields: [],

    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 120,
        },
        {
          id: "name",
          header: "Name",
          accessorKey: "name",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "description",
          header: "Description",
          accessorKey: "description",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "status",
          header: "Status",
          accessorKey: "status",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "formType",
          header: "Form Type",
          accessorKey: "formType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "projectType",
          header: "Project Type",
          accessorKey: "projectType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        // {
        //   id: "version",
        //   header: "Version",
        //   accessorKey: "version",
        //   type: "text",
        //   enableSorting: true,
        //   enableColumnFilter: true,
        //   enablePinning: false,
        //   width: 200,
        // },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Form",
      cancelButtonText: "Cancel",
      successMessage: "Form saved successfully!",
      errorMessage: "Failed to save form. Please try again.",
      redirectAfterSubmit: "/forms",
      addLabel: "Create Form",
    },
  },

  // User entity configuration
  User: {
    // Base entity information
    id: "users",
    entityName: "User",
    title: "Users",
    description: "Manage your users",
    endpoints: {
      list: "/users",
      get: "/users",
      create: "/users",
      update: "/users",
      delete: "/users",
    },
    fields: [
      {
        id: "firstName",
        name: "firstName",
        type: "text",
        label: "First Name",
      },
      {
        id: "lastName",
        name: "lastName",
        type: "text",
        label: "Last Name",
      },
      {
        id: "email",
        name: "email",
        type: "text",
        label: "Email",
        required: true,
      },
      {
        id: "role",
        name: "role",
        type: "select",
        label: "Role",
        dynamicOptionsConfig: {
          resolver: async (context) => {
            try {
              const response = await context.apiClient.get<
                { id: number; name: string; slug: string }[]
              >("/roles");
              const roles = response.data;
              return roles.map((role) => ({
                label: role.name,
                value: role.slug,
              }));
            } catch (error) {
              console.log("🚀 ~ error:", error);
              return [];
            }
          },
          // No dependencies - this field loads roles independently
          cacheKey: () => "all-roles", // Cache all roles with a simple key
        },
      },
      {
        id: "phone",
        name: "phone",
        type: "text",
        label: "Telephone number",
      },
    ],
    // Form view configuration
    formConfig: {
      submitButtonText: "Save User",
      cancelButtonText: "Cancel",
      successMessage: "User saved successfully!",
      errorMessage: "Failed to save user. Please try again.",
      redirectAfterSubmit: "/users",
      addLabel: "Register User",
    },
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 120,
        },
        {
          id: "firstName",
          header: "User Name",
          accessorKey: "firstName",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "lastName",
          header: "Last Name",
          accessorKey: "lastName",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "role",
          header: "Role",
          accessorKey: "role",
          type: "text",
          enableSorting: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "email",
          header: "Email",
          accessorKey: "email",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "phone",
          header: "Telephone number",
          accessorKey: "phone",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "firstName", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
    },
  },

  // User entity configuration
  UserProject: {
    // Base entity information
    id: "project-user-assignment",
    entityName: "UserProject",
    title: "Users assigned to the project",
    description: "Attach user to project",
    endpoints: {
      list: "/project-user-assignments",
      get: "/project-user-assignments",
      create: "/project-user-assignments",
      delete: "/project-user-assignments",
      update: "/project-user-assignments",
    },
    fields: [
      {
        id: "projectId",
        name: "projectId",
        type: "text",
        label: "Project ID",
        disabled: true,
        autoFillConfig: {
          resolver: () => {
            const projectId = window.location.pathname.split("/")[3];
            return Promise.resolve(projectId || "");
          },
          onlyOnInitialLoad: true,
        },
      },
      // TODO: Uncomment when input dependency is implemented
      {
        id: "role",
        name: "role",
        type: "select",
        label: "Role",
        required: true,
        options: [
          { value: "4", label: "Applicant" },
          { value: "5", label: "Assessor" },
          { value: "7", label: "Delivery partner" },
        ],
        defaultValue: "4",
      },
      {
        id: "userId",
        name: "userId",
        type: "select",
        label: "User ID",
        required: true,
        dynamicOptionsConfig: {
          resolver: async (context) => {
            try {
              // Get the selected role value (context will handle default values automatically)
              const selectedRole = context.getFieldValue("role");

              // Map role IDs to role names for API query
              const roleMap: Record<string, string> = {
                "4": "applicant",
                "5": "assessor",
                "7": "delivery_partner",
              };

              const roleName = roleMap[selectedRole] || "applicant";

              const response = await context.apiClient.get<
                GetAllResponses<User>
              >(`/users?role=${roleName}`);

              const users = response.data.content;

              // Handle empty array case
              if (!users || users.length === 0) {
                return [
                  {
                    label: `No users found with ${roleName} role`,
                    value: "no-users-found",
                  },
                ];
              }

              return users.map((user: any) => ({
                label: `${user.firstName} ${user.lastName} - ${user.role}`,
                value: user.id.toString(),
              }));
            } catch (error) {
              console.log("🚀 ~ error:", error);
              // Return a meaningful error option instead of empty array
              return [
                {
                  label: "Error loading users - please try again",
                  value: "error-loading-users",
                },
              ];
            }
          },
          dependsOn: ["role"],
          clearOnEmptyDependency: false, // Don't clear when role is empty, use default instead
          defaultOptions: [
            {
              label: "Loading users...",
              value: "loading",
            },
          ],
          cacheKey: (context) => {
            const selectedRole = context.getFieldValue("role");
            return `users-by-role-${selectedRole}`;
          },
        },
      },
    ],
    // Form view configuration
    formConfig: {
      submitButtonText: "Save User",
      cancelButtonText: "Cancel",
      successMessage: "User attached to project successfully!",
      errorMessage: "Failed to attach user to project. Please try again.",
      redirectAfterSubmit: "/projects",
      addLabel: "Attach User To Project",
    },
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 120,
        },
        {
          id: "firstName",
          header: "First Name",
          accessorKey: "firstName",
          type: "text",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 200,
        },
        {
          id: "lastName",
          header: "Last Name",
          accessorKey: "lastName",
          type: "text",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 200,
        },
        {
          id: "role",
          header: "Role",
          accessorKey: "role",
          type: "date",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 200,
        },
        {
          id: "email",
          header: "Email",
          accessorKey: "email",
          type: "date",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 200,
        },
        {
          id: "assignedAt",
          header: "Assigned At",
          accessorKey: "assignedAt",
          type: "date",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 200,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "firstName", desc: false }],
      enableGlobalFilter: false,
      enableColumnFilters: false,
      enablePinning: false,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
      actions: [
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
    },
  },

  // Add more entity configurations as needed
};

/**
 * Get the base entity configuration
 */
export function getEntityConfig(entityName: string): EntityConfig | null {
  // First try direct lookup (exact match)

  if (entityConfigs[entityName]) {
    return entityConfigs[entityName];
  }

  // Try with first letter capitalized (for case-insensitive lookup)
  const normalizedEntityName =
    entityName.charAt(0).toUpperCase() + entityName.slice(1);
  if (entityConfigs[normalizedEntityName]) {
    return entityConfigs[normalizedEntityName];
  }

  // get entityConfig keys
  const entityConfigKeys = Object.keys(entityConfigs).filter(
    (key) => key.toLowerCase() === entityName.toLowerCase()
  );

  if (entityConfigKeys.length > 0) {
    return entityConfigs[entityConfigKeys[0]];
  }

  return null;
}

/**
 * Create a list page configuration from the base entity configuration
 */
export function createListPageConfig(
  entityName: string
): ListPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "list",
    endpoints: config.endpoints,
    title: config.title,
    description: config.description,
    entityName: config.entityName,
    columns: config.listConfig.columns,
    actions: config.listConfig.actions,
    defaultPageSize: config.listConfig.defaultPageSize,
    enableGlobalFilter: config.listConfig.enableGlobalFilter,
    enableColumnFilters: config.listConfig.enableColumnFilters,
    enablePinning: config.listConfig.enablePinning,
    createFormConfig: createFormPageConfig(entityName) || undefined,
    defaultVisibilityState: config.listConfig.defaultVisibilityState,
    defaultPinnedColumns: config.listConfig.defaultPinnedColumns,
    // New: Include parameter and custom dialog configurations
    parameterConfig: config.parameterConfig,
    customDialogs: config.customDialogs,
    contextTransform: config.contextTransform,
    // Row selection and bulk actions
    enableRowSelection: config.listConfig.enableRowSelection,
    enableMultiRowSelection: config.listConfig.enableMultiRowSelection,
    bulkActions: config.listConfig.bulkActions,
    addLabel: config.formConfig?.addLabel,
  };
}

/**
 * Create a form page configuration from the base entity configuration
 */
export function createFormPageConfig(
  entityName: string
): FormPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "form",
    title: `${config.entityName} Details`,
    endpoints: config.endpoints,
    description: `Edit ${config.entityName.toLowerCase()} information`,
    entityName: config.entityName,
    fields: config.fields,
    submitButtonText: config.formConfig.submitButtonText,
    cancelButtonText: config.formConfig.cancelButtonText,
    successMessage: config.formConfig.successMessage,
    errorMessage: config.formConfig.errorMessage,
    redirectAfterSubmit: config.formConfig.redirectAfterSubmit,
  };
}

/**
 * Create a parameterized list page configuration
 */
export function createParameterizedListPageConfig(
  entityName: string,
  parameterConfig: ParameterConfig,
  customDialogs?: CustomDialogsConfig,
  contextTransform?: (params: Record<string, string>) => Record<string, any>
): ListPageConfig | null {
  const baseConfig = createListPageConfig(entityName);
  if (!baseConfig) return null;

  return {
    ...baseConfig,
    parameterConfig,
    customDialogs,
    contextTransform,
  };
}

/**
 * Create a list page configuration with custom dialogs
 */
export function createCustomDialogListPageConfig(
  entityName: string,
  customDialogs: CustomDialogsConfig
): ListPageConfig | null {
  const baseConfig = createListPageConfig(entityName);
  if (!baseConfig) return null;

  return {
    ...baseConfig,
    customDialogs,
  };
}

/**
 * Utility to create parameter configuration for common patterns
 */
export function createParameterConfig(
  options: ParameterConfig
): ParameterConfig {
  return {
    required: options.required || [],
    optional: options.optional || [],
    pathPattern: options.pathPattern,
    validation: options.validation || {},
    useQueryParams: options.useQueryParams,
  };
}

/**
 * Common parameter validation functions
 */
export const parameterValidators = {
  isUUID: (value: string) =>
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
      value
    ),
  isNumeric: (value: string) => /^\d+$/.test(value),
  isAlphanumeric: (value: string) => /^[a-zA-Z0-9]+$/.test(value),
  minLength: (min: number) => (value: string) => value.length >= min,
  maxLength: (max: number) => (value: string) => value.length <= max,
};

export default entityConfigs;
