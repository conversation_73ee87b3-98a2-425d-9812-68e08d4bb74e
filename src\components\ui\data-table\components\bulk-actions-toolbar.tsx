
import { Button } from "@/components/ui/button";
import { BulkActionConfig } from "@/lib/types/page-config";
import { LucideIcon } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface BulkActionsToolbarProps {
  selectedRowIds: string[];
  selectedRows: any[];
  bulkActions: BulkActionConfig[];
  onClearSelection: () => void;
}

export function BulkActionsToolbar({
  selectedRowIds,
  selectedRows,
  bulkActions,
  onClearSelection,
}: BulkActionsToolbarProps) {
  if (selectedRowIds.length === 0 || !bulkActions || bulkActions.length === 0) {
    return null;
  }

  const handleBulkAction = async (action: BulkActionConfig) => {
    try {
      await action.handler(selectedRowIds, selectedRows);
      // Optionally clear selection after action
      // onClearSelection();
    } catch (error) {
      console.error(`Error executing bulk action ${action.id}:`, error);
    }
  };

  const renderActionButton = (action: BulkActionConfig) => {
    const IconComponent = action.icon as LucideIcon;

    const button = (
      <Button
        key={action.id}
        variant={action.variant || "default"}
        size={action.size || "sm"}
        onClick={() => handleBulkAction(action)}
        className="flex items-center gap-2"
      >
        {IconComponent && <IconComponent className="h-4 w-4" />}
        {action.label}
      </Button>
    );

    // Wrap in confirmation dialog if required
    if (action.requireConfirmation) {
      return (
        <AlertDialog key={action.id}>
          <AlertDialogTrigger asChild>
            <Button
              variant={action.variant || "default"}
              size={action.size || "sm"}
              className="flex items-center gap-2"
            >
              {IconComponent && <IconComponent className="h-4 w-4" />}
              {action.label}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Action</AlertDialogTitle>
              <AlertDialogDescription>
                {action.confirmationMessage ||
                  `Are you sure you want to ${action.label.toLowerCase()} ${selectedRowIds.length} selected item(s)?`}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => handleBulkAction(action)}>
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    }

    return button;
  };

  return (
    <div className="flex items-center justify-between p-4 bg-muted/50 border rounded-lg">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">
          {selectedRowIds.length} item{selectedRowIds.length !== 1 ? 's' : ''} selected
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="text-muted-foreground hover:text-foreground"
        >
          Clear selection
        </Button>
      </div>

      <div className="flex items-center gap-2">
        {bulkActions.map(renderActionButton)}
      </div>
    </div>
  );
}
